# 紧急修复指南

## 问题分析

财务板块报错500，很可能是因为：
1. 数据库迁移失败，companyCode字段没有正确添加
2. 代码期望companyCode字段存在，但数据库中没有
3. companies表不存在或没有编码为'01'的公司记录

## 立即修复步骤

### 步骤1：诊断当前状态
```sql
-- 在数据库中运行诊断脚本
\i src/migrations/diagnose-database-status.sql
```

### 步骤2：根据诊断结果选择修复方案

#### 方案A：如果companyCode字段不存在
```sql
-- 运行修复版迁移脚本
\i src/migrations/add-company-code-to-asset-details-fixed.sql
```

#### 方案B：如果companies表不存在或缺少'01'公司
```sql
-- 确保companies表存在并有默认公司
INSERT INTO companies (code, name, "isDeleted", "createdAt", "updatedAt") 
VALUES ('01', '默认公司', false, NOW(), NOW())
ON CONFLICT (code) DO NOTHING;
```

#### 方案C：紧急回滚（如果需要立即恢复服务）
```sql
-- 回滚所有companyCode字段
\i src/migrations/rollback-company-code-fields.sql
```

### 步骤3：临时代码修复（如果数据库修复需要时间）

如果数据库修复需要时间，可以临时修改代码让服务能够运行：

1. **临时禁用公司验证**：
   - 在所有服务的validateCompany方法中添加try-catch
   - 暂时跳过公司验证

2. **临时修改查询**：
   - 移除JOIN companies表的查询
   - 暂时不返回companyName字段

## 具体修复命令

### 数据库连接
```bash
# 连接到生产数据库
psql -h 43.138.236.92 -U postgres -d manager
```

### 快速检查
```sql
-- 检查表是否存在
\dt *details

-- 检查companyCode字段
\d expense_details
\d rd_cost_details
\d rental_asset_details
\d operating_asset_details
\d fixed_asset_details

-- 检查companies表
\d companies
SELECT * FROM companies WHERE code = '01';
```

### 快速修复
```sql
-- 如果companies表存在但没有'01'公司，添加它
INSERT INTO companies (code, name, "isDeleted", "createdAt", "updatedAt") 
VALUES ('01', '默认公司', false, NOW(), NOW())
ON CONFLICT (code) DO NOTHING;

-- 如果companyCode字段不存在，添加它们
-- （运行 add-company-code-to-asset-details-fixed.sql）
```

## 验证修复

修复后，检查以下内容：
1. 所有明细表都有companyCode字段
2. companies表存在且有编码'01'的公司
3. 财务相关API能正常访问
4. 创建新记录时companyCode验证正常

## 预防措施

1. **备份数据库**：在执行任何迁移前先备份
2. **测试环境验证**：先在测试环境执行迁移
3. **分步执行**：将复杂迁移分解为多个小步骤
4. **监控日志**：密切关注应用日志和数据库日志

## 联系支持

如果问题仍然存在，请提供：
1. 数据库诊断脚本的输出结果
2. 应用程序的错误日志
3. 具体的500错误信息
