# 剩余服务更新指南

## 需要更新的服务文件

### 1. rental-assets/rental-assets.service.ts
- 添加 CompaniesService 导入和注入
- 添加 validateCompany 方法
- 更新 createDetail 方法，添加公司验证
- 更新创建明细记录时包含 companyCode
- 更新分页查询，JOIN companies表并返回companyName

### 2. operating-assets/operating-assets.service.ts
- 添加 CompaniesService 导入和注入
- 添加 validateCompany 方法
- 更新 createDetail 方法，添加公司验证
- 更新创建明细记录时包含 companyCode
- 更新分页查询，JOIN companies表并返回companyName

### 3. fixed-assets/fixed-assets.service.ts
- 添加 CompaniesService 导入和注入
- 添加 validateCompany 方法
- 更新 createDetail 方法，添加公司验证
- 更新创建明细记录时包含 companyCode
- 更新分页查询，JOIN companies表并返回companyName

## 通用模式

### 导入和注入
```typescript
import { CompaniesService } from '../companies/companies.service';

constructor(
  // ... 其他注入
  private readonly companiesService: CompaniesService,
) {}
```

### 验证方法
```typescript
// 验证公司是否存在
private async validateCompany(
  companyCode: string,
): Promise<{ code: string; name: string }> {
  if (!companyCode || companyCode.trim() === '') {
    throw new BadRequestException('公司编码不能为空');
  }

  const company = await this.companiesService.findByCode(companyCode);
  if (!company) {
    throw new BadRequestException(`公司 ${companyCode} 不存在`);
  }

  return { code: company.code, name: company.name };
}
```

### 创建方法更新
```typescript
// 解构时添加 companyCode
const { companyCode, ...otherFields } = createDetailDto;

// 验证公司
const company = await this.validateCompany(companyCode);

// 创建记录时添加 companyCode
const detail = queryRunner.manager.create(EntityDetail, {
  // ... 其他字段
  companyCode: company.code,
  // ... 其他字段
});
```

### 查询方法更新
```typescript
const queryBuilder = this.detailRepository
  .createQueryBuilder('detail')
  .leftJoin('companies', 'company', 'company.code = detail.companyCode')
  .addSelect('company.name', 'companyName')
  .where('detail.isDeleted = false')
  .orderBy(`detail.${sortBy}`, sortOrder as 'ASC' | 'DESC');

// 使用 getRawAndEntities() 获取结果
const result = await queryBuilder.getRawAndEntities();
const details = result.entities.map((detail, index) => ({
  ...detail,
  companyName: result.raw[index]?.companyName || null,
}));
```

## 执行步骤

1. 先运行数据库迁移脚本
2. 更新所有服务文件
3. 测试构建
4. 测试API功能

## 数据库迁移

执行 `src/migrations/add-company-code-to-asset-details.sql` 文件中的SQL语句。
