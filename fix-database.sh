#!/bin/bash

# 一键数据库修复脚本
# 使用方法: chmod +x fix-database.sh && ./fix-database.sh

set -e  # 遇到错误立即退出

# 数据库连接信息
DB_HOST="*************"
DB_USER="postgres"
DB_NAME="manager"
DB_PASSWORD="54188"

echo "🚀 开始修复数据库..."
echo "连接到数据库: $DB_HOST/$DB_NAME"

# 设置PGPASSWORD环境变量，避免重复输入密码
export PGPASSWORD=$DB_PASSWORD

# 创建临时SQL文件
TEMP_SQL="/tmp/fix_database.sql"

cat > $TEMP_SQL << 'EOF'
-- 一键修复脚本：添加公司编码字段
-- 自动检查并修复所有问题

\echo '🔍 开始诊断数据库状态...'

-- 1. 检查并确保companies表存在且有默认公司
DO $$
BEGIN
    -- 确保有编码为'01'的公司
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'companies') THEN
        INSERT INTO companies (code, name, "isDeleted", "createdAt", "updatedAt") 
        VALUES ('01', '默认公司', false, NOW(), NOW())
        ON CONFLICT (code) DO NOTHING;
        RAISE NOTICE '✅ 默认公司(01)已确保存在';
    ELSE
        RAISE EXCEPTION '❌ companies表不存在！请先创建companies表';
    END IF;
END $$;

-- 2. 为支出明细表添加公司编码字段
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'expense_details') THEN
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'expense_details' AND column_name = 'companyCode') THEN
            ALTER TABLE expense_details ADD COLUMN "companyCode" VARCHAR(50);
            UPDATE expense_details SET "companyCode" = '01' WHERE "companyCode" IS NULL;
            ALTER TABLE expense_details ALTER COLUMN "companyCode" SET NOT NULL;
            RAISE NOTICE '✅ expense_details表已添加companyCode字段';
        ELSE
            UPDATE expense_details SET "companyCode" = '01' WHERE "companyCode" IS NULL;
            RAISE NOTICE '✅ expense_details表的companyCode字段已存在';
        END IF;
    ELSE
        RAISE NOTICE '⚠️  expense_details表不存在，跳过';
    END IF;
END $$;

-- 3. 为研发成本明细表添加公司编码字段
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'rd_cost_details') THEN
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'rd_cost_details' AND column_name = 'companyCode') THEN
            ALTER TABLE rd_cost_details ADD COLUMN "companyCode" VARCHAR(50);
            UPDATE rd_cost_details SET "companyCode" = '01' WHERE "companyCode" IS NULL;
            ALTER TABLE rd_cost_details ALTER COLUMN "companyCode" SET NOT NULL;
            RAISE NOTICE '✅ rd_cost_details表已添加companyCode字段';
        ELSE
            UPDATE rd_cost_details SET "companyCode" = '01' WHERE "companyCode" IS NULL;
            RAISE NOTICE '✅ rd_cost_details表的companyCode字段已存在';
        END IF;
    ELSE
        RAISE NOTICE '⚠️  rd_cost_details表不存在，跳过';
    END IF;
END $$;

-- 4. 为租赁资产明细表添加公司编码字段
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'rental_asset_details') THEN
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'rental_asset_details' AND column_name = 'companyCode') THEN
            ALTER TABLE rental_asset_details ADD COLUMN "companyCode" VARCHAR(50);
            UPDATE rental_asset_details SET "companyCode" = '01' WHERE "companyCode" IS NULL;
            ALTER TABLE rental_asset_details ALTER COLUMN "companyCode" SET NOT NULL;
            RAISE NOTICE '✅ rental_asset_details表已添加companyCode字段';
        ELSE
            UPDATE rental_asset_details SET "companyCode" = '01' WHERE "companyCode" IS NULL;
            RAISE NOTICE '✅ rental_asset_details表的companyCode字段已存在';
        END IF;
    ELSE
        RAISE NOTICE '⚠️  rental_asset_details表不存在，跳过';
    END IF;
END $$;

-- 5. 为运营资产明细表添加公司编码字段
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'operating_asset_details') THEN
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'operating_asset_details' AND column_name = 'companyCode') THEN
            ALTER TABLE operating_asset_details ADD COLUMN "companyCode" VARCHAR(50);
            UPDATE operating_asset_details SET "companyCode" = '01' WHERE "companyCode" IS NULL;
            ALTER TABLE operating_asset_details ALTER COLUMN "companyCode" SET NOT NULL;
            RAISE NOTICE '✅ operating_asset_details表已添加companyCode字段';
        ELSE
            UPDATE operating_asset_details SET "companyCode" = '01' WHERE "companyCode" IS NULL;
            RAISE NOTICE '✅ operating_asset_details表的companyCode字段已存在';
        END IF;
    ELSE
        RAISE NOTICE '⚠️  operating_asset_details表不存在，跳过';
    END IF;
END $$;

-- 6. 为固定资产明细表添加公司编码字段
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'fixed_asset_details') THEN
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'fixed_asset_details' AND column_name = 'companyCode') THEN
            ALTER TABLE fixed_asset_details ADD COLUMN "companyCode" VARCHAR(50);
            UPDATE fixed_asset_details SET "companyCode" = '01' WHERE "companyCode" IS NULL;
            ALTER TABLE fixed_asset_details ALTER COLUMN "companyCode" SET NOT NULL;
            RAISE NOTICE '✅ fixed_asset_details表已添加companyCode字段';
        ELSE
            UPDATE fixed_asset_details SET "companyCode" = '01' WHERE "companyCode" IS NULL;
            RAISE NOTICE '✅ fixed_asset_details表的companyCode字段已存在';
        END IF;
    ELSE
        RAISE NOTICE '⚠️  fixed_asset_details表不存在，跳过';
    END IF;
END $$;

\echo '🎉 数据库修复完成！'

-- 最终验证
\echo '📋 最终验证结果：'
SELECT 
    table_name,
    CASE WHEN EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE information_schema.columns.table_name = t.table_name 
        AND column_name = 'companyCode'
    ) THEN '✅ 已添加' ELSE '❌ 缺失' END as companyCode_status
FROM (
    VALUES 
    ('expense_details'),
    ('rd_cost_details'), 
    ('rental_asset_details'),
    ('operating_asset_details'),
    ('fixed_asset_details')
) AS t(table_name)
WHERE EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE information_schema.tables.table_name = t.table_name
);
EOF

echo "📝 执行数据库修复SQL..."
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -f $TEMP_SQL

# 清理临时文件
rm -f $TEMP_SQL

echo "✅ 数据库修复完成！"
echo "🔄 请重启您的应用服务以使更改生效"
