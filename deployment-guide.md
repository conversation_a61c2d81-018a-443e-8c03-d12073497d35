# 🚀 一键部署指南

## 立即修复数据库问题

### 1. 上传修复脚本到服务器
```bash
# 将 fix-database.sh 上传到服务器的项目目录
scp fix-database.sh root@43.138.236.92:/path/to/your/project/
```

### 2. 执行数据库修复
```bash
# 在服务器上执行
chmod +x fix-database.sh
./fix-database.sh
```

这个脚本会：
- ✅ 自动连接数据库（密码已内置）
- ✅ 检查并添加缺失的 companyCode 字段
- ✅ 确保默认公司(01)存在
- ✅ 更新所有现有数据
- ✅ 显示修复结果

## 简化部署流程

### 1. 使用新的简化部署脚本
```bash
# 将 deploy-production-simple.sh 上传到服务器
scp deploy-production-simple.sh root@43.138.236.92:/path/to/your/project/

# 在服务器上执行
chmod +x deploy-production-simple.sh
./deploy-production-simple.sh
```

### 2. 简化部署脚本的优势
- 🔑 **密码自动填入** - 不再需要重复输入数据库密码
- ⚡ **跳过复杂检查** - 移除了耗时的表检查和验证
- 🎯 **专注核心功能** - 只保留必要的部署步骤
- 📝 **简化日志** - 减少冗余的输出信息

## 脚本对比

| 功能 | 原版脚本 | 简化版脚本 |
|------|----------|------------|
| 密码输入 | 需要多次手动输入 | ✅ 自动填入 |
| 表检查 | 复杂的表验证 | ✅ 跳过 |
| 迁移验证 | 详细验证脚本 | ✅ 简化 |
| API测试 | 多个API测试 | ✅ 基础测试 |
| 错误处理 | 严格错误退出 | ✅ 容错继续 |

## 紧急恢复

如果修复后仍有问题，可以使用回滚脚本：

```bash
# 上传回滚脚本
scp src/migrations/rollback-company-code-fields.sql root@43.138.236.92:/tmp/

# 执行回滚
export PGPASSWORD=54188
psql -h 43.138.236.92 -U postgres -d manager -f /tmp/rollback-company-code-fields.sql
```

## 验证修复结果

修复完成后，检查以下内容：

1. **数据库字段检查**：
```sql
-- 连接数据库
export PGPASSWORD=54188
psql -h 43.138.236.92 -U postgres -d manager

-- 检查字段是否存在
\d expense_details
\d rd_cost_details
\d rental_asset_details
\d operating_asset_details
\d fixed_asset_details
```

2. **API测试**：
```bash
# 测试财务相关API
curl http://43.138.236.92:3000/api/expenses/details
curl http://43.138.236.92:3000/api/rd-costs/details
```

3. **服务状态**：
```bash
pm2 status
pm2 logs web-manager-backend --lines 20
```

## 常见问题解决

### Q: 脚本执行权限问题
```bash
chmod +x fix-database.sh
chmod +x deploy-production-simple.sh
```

### Q: 数据库连接失败
检查：
- 数据库服务是否运行
- 网络连接是否正常
- 密码是否正确

### Q: PM2 进程启动失败
```bash
pm2 delete all
pm2 start ecosystem.config.js --env production
```

### Q: 端口被占用
```bash
# 查看端口占用
netstat -tuln | grep 3000
# 杀死占用进程
sudo fuser -k 3000/tcp
```

## 联系支持

如果问题仍然存在，请提供：
1. 修复脚本的完整输出
2. PM2 日志：`pm2 logs web-manager-backend`
3. 具体的错误信息
