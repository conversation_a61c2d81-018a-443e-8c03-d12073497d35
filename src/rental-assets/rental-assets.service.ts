import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import * as ExcelJS from 'exceljs';
import axios from 'axios';
import { RentalAsset } from './entities/rental-asset.entity';
import {
  RentalAssetDetail,
  RentalAssetType,
} from './entities/rental-asset-detail.entity';
import { CreateRentalAssetDetailDto } from './dto/create-rental-asset-detail.dto';
import { UpdateRentalAssetDetailDto } from './dto/update-rental-asset-detail.dto';
import { QueryRentalAssetDetailsDto } from './dto/query-rental-asset-details.dto';
import { CompaniesService } from '../companies/companies.service';

// 内部处理接口
interface ProcessedExportDto {
  startTime?: string;
  endTime?: string;
  detailIds?: string[];
  type?: RentalAssetType;
}

// 租赁资产类型中文映射
const RENTAL_ASSET_TYPE_LABELS = {
  [RentalAssetType.SHORT_TERM]: '短期租赁',
  [RentalAssetType.LONG_TERM]: '长期租赁',
};

@Injectable()
export class RentalAssetsService {
  private readonly logger = new Logger(RentalAssetsService.name);

  constructor(
    @InjectRepository(RentalAsset)
    private rentalAssetRepository: Repository<RentalAsset>,
    @InjectRepository(RentalAssetDetail)
    private rentalAssetDetailRepository: Repository<RentalAssetDetail>,
    private dataSource: DataSource,
    private readonly companiesService: CompaniesService,
  ) {}

  // 获取或创建租赁资产记录（系统只有一条记录）
  private async getOrCreateRentalAsset(): Promise<RentalAsset> {
    let rentalAsset = await this.rentalAssetRepository.findOne({
      where: { isDeleted: false },
    });

    if (!rentalAsset) {
      rentalAsset = this.rentalAssetRepository.create({
        totalAmount: 0,
        createDate: new Date().toISOString().split('T')[0],
      });
      await this.rentalAssetRepository.save(rentalAsset);
      this.logger.log('Created new rental asset record');
    }

    return rentalAsset;
  }

  // 验证公司是否存在
  private async validateCompany(
    companyCode: string,
  ): Promise<{ code: string; name: string }> {
    if (!companyCode || companyCode.trim() === '') {
      throw new BadRequestException('公司编码不能为空');
    }

    const company = await this.companiesService.findByCode(companyCode);
    if (!company) {
      throw new BadRequestException(`公司 ${companyCode} 不存在`);
    }

    return { code: company.code, name: company.name };
  }

  // 新增租赁资产明细
  async createDetail(
    createDetailDto: CreateRentalAssetDetailDto,
  ): Promise<void> {
    const { companyCode, type, amount, screenshot, remark, createDate } =
      createDetailDto;

    // 验证公司
    const company = await this.validateCompany(companyCode);

    this.logger.log(
      `Creating rental asset detail with type: ${type}, amount: ${amount}`,
    );

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 获取或创建租赁资产记录
      const rentalAsset = await this.getOrCreateRentalAsset();

      // 如果没有提供 createDate，使用当前日期
      const finalCreateDate =
        createDate || new Date().toISOString().split('T')[0];

      // 创建明细记录
      const detail = queryRunner.manager.create(RentalAssetDetail, {
        rentalAssetId: rentalAsset.id,
        companyCode: company.code,
        type,
        amount,
        screenshot,
        remark,
        createDate: finalCreateDate,
      });

      await queryRunner.manager.save(detail);

      // 更新总金额
      await queryRunner.manager.update(
        RentalAsset,
        { id: rentalAsset.id },
        {
          totalAmount: () => `"totalAmount" + ${amount}`,
        },
      );

      await queryRunner.commitTransaction();
      this.logger.log(
        `Rental asset detail created successfully with type: ${type}, amount: ${amount}`,
      );
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // 分页查询租赁资产明细
  async findAllDetails(queryDto: QueryRentalAssetDetailsDto) {
    const {
      page = 1,
      pageSize = 10,
      type,
      startTime,
      endTime,
      search,
      sortBy = 'createDate',
      sortOrder = 'DESC',
    } = queryDto;

    this.logger.log(
      `Querying rental asset details: page=${page}, pageSize=${pageSize}, type=${type}`,
    );

    // 获取租赁资产总金额
    const rentalAsset = await this.getOrCreateRentalAsset();

    // 处理时间参数
    let processedStartTime = startTime;
    let processedEndTime = endTime;

    if (startTime) {
      // 如果是日期格式（如 2025-05-01），转换为当天开始时间
      if (startTime.length === 10) {
        processedStartTime = `${startTime}T00:00:00.000Z`;
      }
    }

    if (endTime) {
      // 如果是日期格式（如 2025-05-31），转换为当天结束时间
      if (endTime.length === 10) {
        processedEndTime = `${endTime}T23:59:59.999Z`;
      }
    }

    // 构建查询条件
    const queryBuilder = this.rentalAssetDetailRepository
      .createQueryBuilder('detail')
      .where('detail.isDeleted = false');

    // 类型筛选
    if (type) {
      queryBuilder.andWhere('detail.type = :type', { type });
    }

    // 时间范围筛选 - 由于createDate是字符串类型，使用CAST转换为日期进行比较
    if (processedStartTime) {
      queryBuilder.andWhere(
        'CAST(detail.createDate AS DATE) >= CAST(:startTime AS DATE)',
        {
          startTime: processedStartTime,
        },
      );
    }

    if (processedEndTime) {
      queryBuilder.andWhere(
        'CAST(detail.createDate AS DATE) <= CAST(:endTime AS DATE)',
        {
          endTime: processedEndTime,
        },
      );
    }

    // 搜索筛选（备注）
    if (search) {
      queryBuilder.andWhere('detail.remark ILIKE :search', {
        search: `%${search}%`,
      });
    }

    // 排序
    queryBuilder.orderBy(`detail.${sortBy}`, sortOrder as 'ASC' | 'DESC');

    // 处理分页参数
    if (page === 0 && pageSize === 0) {
      // 返回所有数据
      const details = await queryBuilder.getMany();

      // 添加类型标签
      const detailsWithTypeLabel = details.map((detail) => ({
        ...detail,
        typeLabel: RENTAL_ASSET_TYPE_LABELS[detail.type],
      }));

      // 计算查询结果的总金额
      const queryResultTotalAmount = details.reduce(
        (sum, detail) => sum + detail.amount,
        0,
      );

      return {
        details: detailsWithTypeLabel,
        total: details.length,
        page: 0,
        pageSize: 0,
        totalAmount: queryResultTotalAmount,
        systemTotalAmount: rentalAsset.totalAmount,
        startTime: processedStartTime || null,
        endTime: processedEndTime || null,
      };
    }

    // 正常分页查询
    const [details, total] = await queryBuilder
      .skip((page - 1) * pageSize)
      .take(pageSize)
      .getManyAndCount();

    // 计算所有查询条件下的总金额（不分页）
    const allMatchingDetails = await queryBuilder
      .skip(0)
      .take(undefined)
      .getMany();

    const queryResultTotalAmount = allMatchingDetails.reduce(
      (sum, detail) => sum + detail.amount,
      0,
    );

    // 添加类型标签
    const detailsWithTypeLabel = details.map((detail) => ({
      ...detail,
      typeLabel: RENTAL_ASSET_TYPE_LABELS[detail.type],
    }));

    return {
      details: detailsWithTypeLabel,
      total,
      page,
      pageSize,
      totalAmount: queryResultTotalAmount,
      systemTotalAmount: rentalAsset.totalAmount,
      startTime: processedStartTime || null,
      endTime: processedEndTime || null,
    };
  }

  // 获取租赁资产明细详情
  async findDetailById(
    id: string,
  ): Promise<RentalAssetDetail & { typeLabel: string }> {
    this.logger.log(`Finding rental asset detail by id: ${id}`);

    const detail = await this.rentalAssetDetailRepository.findOne({
      where: { id, isDeleted: false },
    });

    if (!detail) {
      throw new NotFoundException('租赁资产明细不存在');
    }

    return {
      ...detail,
      typeLabel: RENTAL_ASSET_TYPE_LABELS[detail.type],
    };
  }

  // 更新租赁资产明细
  async updateDetail(
    id: string,
    updateDetailDto: UpdateRentalAssetDetailDto,
  ): Promise<void> {
    this.logger.log(`Updating rental asset detail: ${id}`);

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 查找现有明细
      const existingDetail = await queryRunner.manager.findOne(
        RentalAssetDetail,
        {
          where: { id, isDeleted: false },
        },
      );

      if (!existingDetail) {
        throw new NotFoundException('租赁资产明细不存在');
      }

      const oldAmount = existingDetail.amount;
      const newAmount = updateDetailDto.amount ?? oldAmount;
      const amountDifference = newAmount - oldAmount;

      // 更新明细记录
      await queryRunner.manager.update(
        RentalAssetDetail,
        { id },
        updateDetailDto,
      );

      // 如果金额有变化，更新总金额
      if (amountDifference !== 0) {
        const rentalAsset = await this.getOrCreateRentalAsset();
        await queryRunner.manager.update(
          RentalAsset,
          { id: rentalAsset.id },
          {
            totalAmount: () => `"totalAmount" + ${amountDifference}`,
          },
        );
      }

      await queryRunner.commitTransaction();
      this.logger.log(`Rental asset detail updated successfully: ${id}`);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // 删除租赁资产明细
  async removeDetail(id: string): Promise<void> {
    this.logger.log(`Removing rental asset detail: ${id}`);

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 查找现有明细
      const existingDetail = await queryRunner.manager.findOne(
        RentalAssetDetail,
        {
          where: { id, isDeleted: false },
        },
      );

      if (!existingDetail) {
        throw new NotFoundException('租赁资产明细不存在');
      }

      // 软删除明细记录
      await queryRunner.manager.update(
        RentalAssetDetail,
        { id },
        {
          isDeleted: true,
        },
      );

      // 从总金额中减去删除的金额
      const rentalAsset = await this.getOrCreateRentalAsset();
      await queryRunner.manager.update(
        RentalAsset,
        { id: rentalAsset.id },
        {
          totalAmount: () => `"totalAmount" - ${existingDetail.amount}`,
        },
      );

      await queryRunner.commitTransaction();
      this.logger.log(`Rental asset detail removed successfully: ${id}`);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // 导出Excel
  async exportToExcel(exportDto?: ProcessedExportDto): Promise<Buffer> {
    this.logger.log('Exporting rental asset details to Excel');

    // 验证：如果没有提供明细ID，必须提供时间范围
    if (!exportDto?.detailIds || exportDto.detailIds.length === 0) {
      if (!exportDto?.startTime || !exportDto?.endTime) {
        throw new BadRequestException(
          '请选择明细进行导出，或提供起始时间和终止时间进行时间范围导出',
        );
      }
    }

    // 获取租赁资产总金额
    const rentalAsset = await this.getOrCreateRentalAsset();

    // 构建查询条件
    const queryBuilder = this.rentalAssetDetailRepository
      .createQueryBuilder('detail')
      .where('detail.isDeleted = false');

    // 时间范围筛选
    if (exportDto?.startTime) {
      queryBuilder.andWhere('detail.createDate >= :startTime', {
        startTime: exportDto.startTime,
      });
    }

    if (exportDto?.endTime) {
      queryBuilder.andWhere('detail.createDate <= :endTime', {
        endTime: exportDto.endTime,
      });
    }

    // 类型筛选
    if (exportDto?.type) {
      queryBuilder.andWhere('detail.type = :type', { type: exportDto.type });
    }

    // 明细ID筛选
    if (exportDto?.detailIds && exportDto.detailIds.length > 0) {
      queryBuilder.andWhere('detail.id IN (:...detailIds)', {
        detailIds: exportDto.detailIds,
      });
    }

    // 排序
    queryBuilder.orderBy('detail.createDate', 'DESC');

    const details = await queryBuilder.getMany();

    this.logger.log(`Found ${details.length} details for export`);
    if (details.length > 0) {
      this.logger.log(
        `First detail ID: ${details[0].id}, Type: ${details[0].type}, Amount: ${details[0].amount}`,
      );
    }

    // 计算选中明细的总金额
    const selectedTotalAmount = details.reduce(
      (sum, detail) => sum + detail.amount,
      0,
    );

    // 生成Excel（支持图片插入）
    try {
      // 创建工作簿
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('租赁资产明细');

      // 设置列宽
      worksheet.columns = [
        { header: '序号', key: 'index', width: 8 },
        { header: '类型', key: 'type', width: 15 },
        { header: '金额', key: 'amount', width: 15 },
        { header: '创建时间', key: 'createdAt', width: 20 },
        { header: '截图', key: 'screenshot', width: 30 },
        { header: '备注', key: 'remark', width: 30 },
      ];

      // 添加标题
      worksheet.mergeCells('A1:F1');
      const titleCell = worksheet.getCell('A1');
      titleCell.value = '租赁资产管理报告';
      titleCell.font = { bold: true, size: 16 };
      titleCell.alignment = { horizontal: 'center' };

      // 添加概况信息
      let currentRow = 3;
      worksheet.getCell(`A${currentRow}`).value = '资产概况';
      worksheet.getCell(`A${currentRow}`).font = { bold: true, size: 14 };
      currentRow++;

      worksheet.getCell(`A${currentRow}`).value = '租赁资产总金额';
      worksheet.getCell(`B${currentRow}`).value =
        `¥${rentalAsset.totalAmount.toFixed(2)}`;
      currentRow++;

      worksheet.getCell(`A${currentRow}`).value = '导出明细总金额';
      worksheet.getCell(`B${currentRow}`).value =
        `¥${selectedTotalAmount.toFixed(2)}`;
      currentRow++;

      worksheet.getCell(`A${currentRow}`).value = '导出明细数量';
      worksheet.getCell(`B${currentRow}`).value = `${details.length}条`;
      currentRow++;

      worksheet.getCell(`A${currentRow}`).value = '导出时间';
      worksheet.getCell(`B${currentRow}`).value = new Date().toLocaleString(
        'zh-CN',
      );
      currentRow += 2;

      // 添加明细记录标题
      worksheet.getCell(`A${currentRow}`).value = '明细记录';
      worksheet.getCell(`A${currentRow}`).font = { bold: true, size: 14 };
      currentRow++;

      // 添加表头
      const headerRow = worksheet.getRow(currentRow);
      headerRow.values = ['序号', '类型', '金额', '创建时间', '截图', '备注'];
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' },
      };
      currentRow++;

      // 添加明细数据和图片
      for (let i = 0; i < details.length; i++) {
        const detail = details[i];
        const row = worksheet.getRow(currentRow);

        // 设置行高以容纳图片
        row.height = 120;

        // 添加基本数据
        row.values = [
          i + 1,
          RENTAL_ASSET_TYPE_LABELS[detail.type],
          `¥${detail.amount.toFixed(2)}`,
          detail.createDate,
          '', // 截图列留空，用于插入图片
          detail.remark || '',
        ];

        // 下载并插入图片
        try {
          if (detail.screenshot) {
            this.logger.log(`Downloading image: ${detail.screenshot}`);

            const imageResponse = await axios.get(detail.screenshot, {
              responseType: 'arraybuffer',
              timeout: 10000,
            });

            const imageBuffer = Buffer.from(imageResponse.data);

            // 添加图片到工作簿
            const imageId = workbook.addImage({
              buffer: imageBuffer,
              extension: 'jpeg',
            });

            // 在截图列插入图片
            worksheet.addImage(imageId, {
              tl: { col: 4, row: currentRow - 1 }, // 从截图列开始
              ext: { width: 200, height: 100 }, // 图片大小
            });

            this.logger.log(`Image inserted successfully for detail ${i + 1}`);
          }
        } catch (imageError) {
          this.logger.warn(
            `Failed to load image for detail ${i + 1}: ${detail.screenshot}`,
            imageError.message,
          );
          // 如果图片加载失败，在截图列显示链接
          row.getCell(5).value = detail.screenshot;
        }

        currentRow++;
      }

      // 设置边框
      const borderStyle = {
        top: { style: 'thin' as const },
        left: { style: 'thin' as const },
        bottom: { style: 'thin' as const },
        right: { style: 'thin' as const },
      };

      // 为表格添加边框
      const tableStartRow = currentRow - details.length - 1;
      const tableEndRow = currentRow - 1;
      for (let row = tableStartRow; row <= tableEndRow; row++) {
        for (let col = 1; col <= 6; col++) {
          worksheet.getCell(row, col).border = borderStyle;
        }
      }

      // 生成Excel缓冲区
      const excelBuffer = await workbook.xlsx.writeBuffer();
      return Buffer.from(excelBuffer);
    } catch (error) {
      this.logger.error('Excel generation failed', error);
      throw new BadRequestException('Excel生成失败');
    }
  }
}
